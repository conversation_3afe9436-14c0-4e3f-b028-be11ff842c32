/* 🌈 Gradient Spinner Loader */
.loader {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: inline-block;
  box-sizing: border-box;
  animation: rotation 1s linear infinite;
  border: 5px solid transparent;
  background: conic-gradient(from 0deg, #30C59B, #00A3FF) border-box;
  -webkit-mask:
    linear-gradient(#fff 0 0) content-box,
    linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
          mask-composite: exclude;
  padding: 5px;
}

@keyframes rotation {
  to {
    transform: rotate(360deg);
  }
}
